"""
图像识别脚本 - 识别"发消息"按钮 (OpenCV版本)
作者: AI Assistant
功能: 使用OpenCV图像处理技术识别聊天界面中的按钮区域
"""

import os
import cv2
import numpy as np
import json
import logging
from typing import List, Tuple, Dict, Optional
from config import IMAGE_CONFIG

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# OpenCV检测配置
OPENCV_CONFIG = {
    'button_min_width': 40,      # 按钮最小宽度
    'button_max_width': 200,     # 按钮最大宽度
    'button_min_height': 20,     # 按钮最小高度
    'button_max_height': 80,     # 按钮最大高度
    'contour_area_min': 800,     # 轮廓最小面积
    'aspect_ratio_min': 1.5,     # 宽高比最小值
    'aspect_ratio_max': 8.0,     # 宽高比最大值
}


class MessageButtonDetectorOpenCV:
    """发消息按钮检测器 (OpenCV版本)"""
    
    def __init__(self):
        """初始化检测器"""
        # 创建输出目录
        os.makedirs(IMAGE_CONFIG['output_dir'], exist_ok=True)
        logger.info("OpenCV检测器初始化成功")
    
    def detect_message_buttons(self, image_path: str) -> List[Dict]:
        """
        检测图片中的发消息按钮
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            检测结果列表，每个元素包含位置和区域信息
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
        logger.info(f"开始处理图片: {image_path}")
        
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图片: {image_path}")
        
        # 检测按钮区域
        button_regions = self._detect_button_regions(image)
        
        # 转换为标准格式
        detected_buttons = []
        for i, region in enumerate(button_regions):
            x, y, w, h = region
            
            # 构建边界框
            bbox = [[x, y], [x + w, y], [x + w, y + h], [x, y + h]]
            
            button_info = {
                'text': f'Button_{i+1}',  # OpenCV版本无法识别文字，使用编号
                'confidence': 0.8,  # 固定置信度
                'bbox': bbox,
                'center': (x + w // 2, y + h // 2),
                'size': (w, h),
                'detection_method': 'opencv_contour'
            }
            detected_buttons.append(button_info)
            logger.info(f"检测到按钮区域: 位置({x}, {y}), 尺寸({w}x{h})")
        
        return detected_buttons
    
    def _detect_button_regions(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """
        使用OpenCV检测按钮区域
        
        Args:
            image: 输入图像
            
        Returns:
            按钮区域列表 [(x, y, w, h), ...]
        """
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 高斯模糊
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 自适应阈值
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2
        )
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        morphed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(morphed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 筛选按钮候选区域
        button_regions = []
        for contour in contours:
            # 获取边界矩形
            x, y, w, h = cv2.boundingRect(contour)
            
            # 检查尺寸约束
            if not self._is_valid_button_size(w, h):
                continue
            
            # 检查轮廓面积
            area = cv2.contourArea(contour)
            if area < OPENCV_CONFIG['contour_area_min']:
                continue
            
            # 检查宽高比
            aspect_ratio = w / h
            if not (OPENCV_CONFIG['aspect_ratio_min'] <= aspect_ratio <= OPENCV_CONFIG['aspect_ratio_max']):
                continue
            
            # 检查位置（通常按钮在图片下方）
            image_height = image.shape[0]
            if y < image_height * 0.3:  # 忽略图片上方30%的区域
                continue
            
            button_regions.append((x, y, w, h))
        
        # 按面积排序，保留最大的几个
        button_regions.sort(key=lambda r: r[2] * r[3], reverse=True)
        return button_regions[:5]  # 最多返回5个候选区域
    
    def _is_valid_button_size(self, width: int, height: int) -> bool:
        """检查是否为有效的按钮尺寸"""
        return (OPENCV_CONFIG['button_min_width'] <= width <= OPENCV_CONFIG['button_max_width'] and
                OPENCV_CONFIG['button_min_height'] <= height <= OPENCV_CONFIG['button_max_height'])
    
    def draw_annotations(self, image_path: str, detected_buttons: List[Dict], 
                        output_path: Optional[str] = None) -> str:
        """
        在图片上绘制检测结果标注
        
        Args:
            image_path: 原始图片路径
            detected_buttons: 检测结果
            output_path: 输出路径（可选）
            
        Returns:
            输出文件路径
        """
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图片用于标注: {image_path}")
        
        # 绘制标注
        for i, button in enumerate(detected_buttons):
            bbox = button['bbox']
            confidence = button['confidence']
            
            # 转换bbox为矩形坐标
            points = np.array(bbox, dtype=np.int32)
            
            # 绘制边界框
            cv2.polylines(image, [points], True, IMAGE_CONFIG['bbox_color'], 
                         IMAGE_CONFIG['bbox_thickness'])
            
            # 绘制文字标签
            label = f"Button_{i+1} ({confidence:.2f})"
            label_pos = (int(bbox[0][0]), int(bbox[0][1]) - 10)
            
            cv2.putText(image, label, label_pos, cv2.FONT_HERSHEY_SIMPLEX,
                       IMAGE_CONFIG['text_scale'], IMAGE_CONFIG['text_color'],
                       IMAGE_CONFIG['text_thickness'])
            
            # 绘制中心点
            center = button['center']
            cv2.circle(image, center, 5, (0, 0, 255), -1)
            
            # 绘制编号
            cv2.putText(image, str(i+1), (center[0]-5, center[1]+5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # 保存标注后的图片
        if output_path is None:
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            output_path = os.path.join(IMAGE_CONFIG['output_dir'], 
                                     f"{base_name}_annotated_opencv.jpg")
        
        cv2.imwrite(output_path, image)
        logger.info(f"标注图片已保存: {output_path}")
        return output_path
    
    def save_results_to_json(self, detected_buttons: List[Dict], 
                           output_path: Optional[str] = None) -> str:
        """
        保存检测结果到JSON文件
        
        Args:
            detected_buttons: 检测结果
            output_path: 输出路径（可选）
            
        Returns:
            输出文件路径
        """
        if output_path is None:
            output_path = os.path.join(IMAGE_CONFIG['output_dir'], 'detection_results_opencv.json')
        
        # 格式化结果数据
        results_data = {
            'detection_method': 'OpenCV',
            'total_buttons': len(detected_buttons),
            'note': 'OpenCV版本基于图像处理检测按钮区域，无法识别具体文字内容',
            'buttons': []
        }
        
        for i, button in enumerate(detected_buttons):
            button_data = {
                'id': i + 1,
                'label': button['text'],
                'confidence': round(button['confidence'], 3),
                'position': {
                    'center_x': button['center'][0],
                    'center_y': button['center'][1],
                    'width': button['size'][0],
                    'height': button['size'][1]
                },
                'bbox_coordinates': button['bbox'],
                'detection_method': button['detection_method']
            }
            results_data['buttons'].append(button_data)
        
        # 保存到JSON文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"检测结果已保存到JSON: {output_path}")
        return output_path
    
    def process_image(self, image_path: str, save_annotated: bool = True, 
                     save_json: bool = True) -> Dict:
        """
        完整处理图片的主方法
        
        Args:
            image_path: 图片路径
            save_annotated: 是否保存标注图片
            save_json: 是否保存JSON结果
            
        Returns:
            处理结果字典
        """
        try:
            # 检测按钮
            detected_buttons = self.detect_message_buttons(image_path)
            
            result = {
                'success': True,
                'detection_method': 'OpenCV',
                'image_path': image_path,
                'total_buttons': len(detected_buttons),
                'buttons': detected_buttons,
                'output_files': {}
            }
            
            # 保存标注图片
            if save_annotated and detected_buttons:
                annotated_path = self.draw_annotations(image_path, detected_buttons)
                result['output_files']['annotated_image'] = annotated_path
            
            # 保存JSON结果
            if save_json and detected_buttons:
                json_path = self.save_results_to_json(detected_buttons)
                result['output_files']['json_results'] = json_path
            
            # 打印结果摘要
            self._print_summary(result)
            
            return result
            
        except Exception as e:
            logger.error(f"处理图片时发生错误: {e}")
            return {
                'success': False,
                'error': str(e),
                'image_path': image_path
            }
    
    def _print_summary(self, result: Dict):
        """打印处理结果摘要"""
        print("\n" + "="*50)
        print("🔍 图像识别结果摘要 (OpenCV)")
        print("="*50)
        print(f"📁 处理图片: {result['image_path']}")
        print(f"🎯 检测到按钮区域数量: {result['total_buttons']}")
        print("ℹ️  注意: OpenCV版本基于图像处理检测按钮区域，无法识别具体文字内容")
        
        if result['total_buttons'] > 0:
            print("\n📍 按钮区域详细信息:")
            for i, button in enumerate(result['buttons'], 1):
                center = button['center']
                size = button['size']
                print(f"  {i}. 区域标签: '{button['text']}'")
                print(f"     位置: ({center[0]}, {center[1]})")
                print(f"     尺寸: {size[0]}×{size[1]} 像素")
                print(f"     置信度: {button['confidence']:.2f}")
                print()
        
        if 'output_files' in result and result['output_files']:
            print("📂 输出文件:")
            for file_type, file_path in result['output_files'].items():
                print(f"  - {file_type}: {file_path}")
        
        print("="*50)


def main():
    """主函数 - 命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description='图像识别脚本 - 识别按钮区域 (OpenCV版本)')
    parser.add_argument('image_path', help='输入图片路径')
    parser.add_argument('--no-annotate', action='store_true', help='不保存标注图片')
    parser.add_argument('--no-json', action='store_true', help='不保存JSON结果')
    parser.add_argument('--output-dir', help='自定义输出目录')
    
    args = parser.parse_args()
    
    # 自定义输出目录
    if args.output_dir:
        IMAGE_CONFIG['output_dir'] = args.output_dir
    
    # 创建检测器并处理图片
    detector = MessageButtonDetectorOpenCV()
    result = detector.process_image(
        image_path=args.image_path,
        save_annotated=not args.no_annotate,
        save_json=not args.no_json
    )
    
    # 返回状态码
    return 0 if result['success'] else 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
