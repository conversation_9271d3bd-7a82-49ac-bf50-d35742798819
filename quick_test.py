"""
快速测试脚本 - 验证基本功能
"""

import os
import sys

def test_imports():
    """测试导入功能"""
    print("🧪 测试模块导入...")
    
    try:
        import cv2
        print("✅ OpenCV导入成功")
        
        import numpy as np
        print("✅ NumPy导入成功")
        
        from PIL import Image
        print("✅ Pillow导入成功")
        
        # 测试PaddleOCR导入（可能较慢）
        print("⏳ 正在导入PaddleOCR...")
        from paddleocr import PaddleOCR
        print("✅ PaddleOCR导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_config():
    """测试配置文件"""
    print("\n🧪 测试配置文件...")
    
    try:
        from config import OCR_CONFIG, TARGET_KEYWORDS, IMAGE_CONFIG, DETECTION_CONFIG
        print("✅ 配置文件导入成功")
        print(f"  - 目标关键词: {TARGET_KEYWORDS}")
        print(f"  - 输出目录: {IMAGE_CONFIG['output_dir']}")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n🧪 测试文件结构...")
    
    required_files = [
        'message_button_detector.py',
        'config.py',
        'requirements.txt',
        'README.md'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - 文件不存在")
            missing_files.append(file)
    
    # 检查样本图片
    doc_dir = "doc"
    if os.path.exists(doc_dir):
        image_files = [f for f in os.listdir(doc_dir) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]
        print(f"✅ 样本图片目录存在，包含 {len(image_files)} 张图片")
    else:
        print(f"⚠️ 样本图片目录不存在: {doc_dir}")
    
    return len(missing_files) == 0

def test_basic_opencv():
    """测试OpenCV基本功能"""
    print("\n🧪 测试OpenCV基本功能...")
    
    try:
        import cv2
        import numpy as np
        
        # 创建测试图片
        test_image = np.zeros((100, 200, 3), dtype=np.uint8)
        cv2.putText(test_image, "Test", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # 保存测试图片
        test_path = "test_image.jpg"
        cv2.imwrite(test_path, test_image)
        
        # 读取测试图片
        loaded_image = cv2.imread(test_path)
        
        if loaded_image is not None:
            print("✅ OpenCV图片读写功能正常")
            # 清理测试文件
            os.remove(test_path)
            return True
        else:
            print("❌ OpenCV图片读取失败")
            return False
            
    except Exception as e:
        print(f"❌ OpenCV测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 快速功能测试")
    print("=" * 40)
    
    tests = [
        ("文件结构", test_file_structure),
        ("配置文件", test_config),
        ("OpenCV功能", test_basic_opencv),
        ("模块导入", test_imports)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️ {test_name} 测试未完全通过")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed >= 3:  # 至少3个测试通过
        print("🎉 基本功能测试通过！")
        print("\n💡 使用说明:")
        print("1. 运行主脚本: python message_button_detector.py <图片路径>")
        print("2. 查看示例: python example_usage.py")
        print("3. 首次运行PaddleOCR会下载模型，请耐心等待")
        return True
    else:
        print("⚠️ 部分功能测试失败，请检查安装")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
