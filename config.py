"""
配置文件 - 图像识别脚本配置参数
"""

# OCR识别配置
OCR_CONFIG = {
    'use_angle_cls': True,  # 是否使用角度分类器
    'lang': 'ch',          # 语言设置：ch(中文), en(英文)
    'use_gpu': False,      # 是否使用GPU加速
    'show_log': False      # 是否显示PaddleOCR日志
}

# 目标文字识别配置
TARGET_KEYWORDS = [
    '发消息',
    '发送消息', 
    '发送',
    '消息',
    'Send',
    'Message',
    '发信息'
]

# 图像处理配置
IMAGE_CONFIG = {
    'output_dir': 'output',           # 输出目录
    'bbox_color': (0, 255, 0),       # 边界框颜色 (B, G, R)
    'bbox_thickness': 2,             # 边界框线条粗细
    'text_color': (255, 0, 0),       # 文字颜色 (B, G, R)
    'text_scale': 0.8,               # 文字大小比例
    'text_thickness': 2              # 文字线条粗细
}

# 识别精度配置
DETECTION_CONFIG = {
    'confidence_threshold': 0.5,     # 置信度阈值
    'similarity_threshold': 0.8,     # 文字相似度阈值
    'min_text_size': 10             # 最小文字尺寸
}
