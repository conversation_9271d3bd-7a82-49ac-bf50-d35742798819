# 图像识别脚本依赖包
# 安装命令: pip install -r requirements.txt

# 核心依赖（所有版本都需要）
opencv-python>=4.5.0
numpy>=1.21.0
Pillow>=8.0.0

# OCR引擎选择（二选一）
# 选项1: PaddleOCR（推荐，中文识别效果更好）
# paddlepaddle>=2.4.0
# paddleocr>=2.6.0
# langchain  # PaddleOCR新版本的依赖

# 选项2: Tesseract OCR（更稳定，依赖更少）
pytesseract>=0.3.8

# 可选依赖（用于更好的性能）
# paddlepaddle-gpu>=2.4.0  # 如果有GPU支持，可以替换paddlepaddle

# 开发和测试依赖
pytest>=6.0.0

# 注意：
# 1. 如果使用PaddleOCR，请取消注释相关行并注释pytesseract
# 2. 如果使用Tesseract，需要安装Tesseract-OCR程序
# 3. Tesseract中文支持需要下载中文语言包
