"""
测试脚本 - 验证图像识别功能
"""

import os
import sys
from message_button_detector import MessageButtonDetector

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能")
    print("-" * 30)
    
    try:
        # 创建检测器
        detector = MessageButtonDetector()
        print("✅ 检测器创建成功")
        
        # 检查输出目录
        output_dir = "output"
        if os.path.exists(output_dir):
            print(f"✅ 输出目录存在: {output_dir}")
        else:
            print(f"📁 创建输出目录: {output_dir}")
            os.makedirs(output_dir, exist_ok=True)
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def test_sample_images():
    """测试样本图片"""
    print("\n🧪 测试样本图片")
    print("-" * 30)
    
    detector = MessageButtonDetector()
    doc_dir = "doc"
    
    if not os.path.exists(doc_dir):
        print(f"⚠️ 样本目录不存在: {doc_dir}")
        return False
    
    # 获取图片文件
    image_files = [f for f in os.listdir(doc_dir) 
                  if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]
    
    if not image_files:
        print(f"⚠️ 在 {doc_dir} 目录中未找到图片文件")
        return False
    
    print(f"📸 找到 {len(image_files)} 张测试图片")
    
    success_count = 0
    for image_file in image_files[:2]:  # 只测试前2张图片
        image_path = os.path.join(doc_dir, image_file)
        print(f"\n测试图片: {image_file}")
        
        try:
            # 仅检测，不保存文件（快速测试）
            buttons = detector.detect_message_buttons(image_path)
            print(f"  ✅ 检测完成，发现 {len(buttons)} 个按钮")
            
            if buttons:
                for i, button in enumerate(buttons, 1):
                    print(f"    {i}. '{button['text']}' (置信度: {button['confidence']:.2f})")
            
            success_count += 1
            
        except Exception as e:
            print(f"  ❌ 检测失败: {e}")
    
    print(f"\n📊 测试结果: {success_count}/{len(image_files[:2])} 成功")
    return success_count > 0

def test_configuration():
    """测试配置功能"""
    print("\n🧪 测试配置功能")
    print("-" * 30)
    
    try:
        from config import OCR_CONFIG, TARGET_KEYWORDS, IMAGE_CONFIG, DETECTION_CONFIG
        
        print("✅ 配置文件导入成功")
        print(f"  - OCR配置: {len(OCR_CONFIG)} 项")
        print(f"  - 目标关键词: {len(TARGET_KEYWORDS)} 个")
        print(f"  - 图像配置: {len(IMAGE_CONFIG)} 项")
        print(f"  - 检测配置: {len(DETECTION_CONFIG)} 项")
        
        # 验证关键配置项
        required_keys = ['use_angle_cls', 'lang', 'use_gpu']
        for key in required_keys:
            if key in OCR_CONFIG:
                print(f"  ✅ {key}: {OCR_CONFIG[key]}")
            else:
                print(f"  ❌ 缺少配置项: {key}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_dependencies():
    """测试依赖包"""
    print("\n🧪 测试依赖包")
    print("-" * 30)
    
    dependencies = [
        ('cv2', 'opencv-python'),
        ('numpy', 'numpy'),
        ('PIL', 'Pillow'),
        ('paddleocr', 'paddleocr')
    ]
    
    missing_deps = []
    
    for module_name, package_name in dependencies:
        try:
            __import__(module_name)
            print(f"✅ {package_name}")
        except ImportError:
            print(f"❌ {package_name} - 未安装")
            missing_deps.append(package_name)
    
    if missing_deps:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_deps)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行测试套件")
    print("=" * 50)
    
    tests = [
        ("依赖包检查", test_dependencies),
        ("配置功能", test_configuration),
        ("基本功能", test_basic_functionality),
        ("样本图片", test_sample_images)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 发生异常: {e}")
            results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！脚本已准备就绪。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置和依赖。")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        print("\n💡 下一步:")
        print("1. 将测试图片放入 doc/ 目录")
        print("2. 运行: python message_button_detector.py doc/your_image.png")
        print("3. 查看 output/ 目录中的结果")
    
    sys.exit(0 if success else 1)
