"""
使用示例 - 图像识别脚本演示
"""

import os
from message_button_detector import MessageButtonDetector

def example_single_image():
    """示例1: 处理单张图片"""
    print("🔍 示例1: 处理单张图片")
    print("-" * 30)
    
    # 创建检测器
    detector = MessageButtonDetector()
    
    # 处理图片（假设doc目录下有测试图片）
    image_path = "doc/wechat_2025-08-05_232102_125.png"
    
    if os.path.exists(image_path):
        result = detector.process_image(image_path)
        
        if result['success']:
            print(f"✅ 成功处理图片，检测到 {result['total_buttons']} 个按钮")
        else:
            print(f"❌ 处理失败: {result.get('error', '未知错误')}")
    else:
        print(f"⚠️ 测试图片不存在: {image_path}")
    
    print()

def example_batch_processing():
    """示例2: 批量处理多张图片"""
    print("🔍 示例2: 批量处理多张图片")
    print("-" * 30)
    
    # 创建检测器
    detector = MessageButtonDetector()
    
    # 获取doc目录下的所有图片
    doc_dir = "doc"
    if os.path.exists(doc_dir):
        image_files = [f for f in os.listdir(doc_dir) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]
        
        print(f"发现 {len(image_files)} 张图片")
        
        total_buttons = 0
        for image_file in image_files:
            image_path = os.path.join(doc_dir, image_file)
            print(f"\n处理: {image_file}")
            
            result = detector.process_image(image_path, save_json=False)
            if result['success']:
                buttons_count = result['total_buttons']
                total_buttons += buttons_count
                print(f"  ✅ 检测到 {buttons_count} 个按钮")
            else:
                print(f"  ❌ 处理失败: {result.get('error', '未知错误')}")
        
        print(f"\n📊 批量处理完成，总共检测到 {total_buttons} 个按钮")
    else:
        print(f"⚠️ 目录不存在: {doc_dir}")
    
    print()

def example_custom_config():
    """示例3: 自定义配置"""
    print("🔍 示例3: 自定义配置")
    print("-" * 30)
    
    # 修改配置
    from config import TARGET_KEYWORDS, IMAGE_CONFIG
    
    # 添加自定义关键词
    original_keywords = TARGET_KEYWORDS.copy()
    TARGET_KEYWORDS.extend(['发送', '提交', 'Submit'])
    
    # 修改输出目录
    original_output_dir = IMAGE_CONFIG['output_dir']
    IMAGE_CONFIG['output_dir'] = 'custom_output'
    
    print(f"自定义关键词: {TARGET_KEYWORDS}")
    print(f"自定义输出目录: {IMAGE_CONFIG['output_dir']}")
    
    # 恢复原始配置
    TARGET_KEYWORDS.clear()
    TARGET_KEYWORDS.extend(original_keywords)
    IMAGE_CONFIG['output_dir'] = original_output_dir
    
    print("配置已恢复")
    print()

def example_api_usage():
    """示例4: API调用方式"""
    print("🔍 示例4: API调用方式")
    print("-" * 30)
    
    detector = MessageButtonDetector()
    
    # 仅检测，不保存文件
    image_path = "doc/wechat_2025-08-05_232102_125.png"
    
    if os.path.exists(image_path):
        # 只进行检测
        buttons = detector.detect_message_buttons(image_path)
        
        print(f"检测结果: {len(buttons)} 个按钮")
        for i, button in enumerate(buttons, 1):
            print(f"  按钮 {i}: '{button['text']}' at {button['center']}")
        
        # 手动保存标注图片
        if buttons:
            output_path = detector.draw_annotations(image_path, buttons, "manual_output.jpg")
            print(f"手动保存标注图片: {output_path}")
    else:
        print(f"⚠️ 测试图片不存在: {image_path}")
    
    print()

if __name__ == "__main__":
    print("🚀 图像识别脚本使用示例")
    print("=" * 50)
    
    # 运行所有示例
    example_single_image()
    example_batch_processing()
    example_custom_config()
    example_api_usage()
    
    print("✅ 所有示例运行完成！")
    print("\n💡 提示:")
    print("1. 确保已安装所有依赖: pip install -r requirements.txt")
    print("2. 将测试图片放在 doc/ 目录下")
    print("3. 运行主脚本: python message_button_detector.py <图片路径>")
