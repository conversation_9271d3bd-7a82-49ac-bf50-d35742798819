Format: https://www.debian.org/doc/packaging-manuals/copyright-format/1.0/
Upstream-Name: pypdfium2
Upstream-Contact: geisserml <<EMAIL>>
Source: https://github.com/pypdfium2-team/pypdfium2

# This file contains copyright info for data files included in our binary wheel packages.
# See also .reuse/dep5 regarding copyright of data files in the project directory.

Files:
    pypdfium2_raw/bindings.py
    pypdfium2_raw/version.json
    pypdfium2_helpers/version.json
Copyright:
    2024 geisserml
    2024 ctypesgen developers
License: Apache-2.0 OR BSD-3-Clause
Comment:
    Auto-generated by ctypesgen/pypdfium2.
    pypdfium2_helpers/... applies only if the helpers module is included.

Files:
    pypdfium2_raw/libpdfium.so
    pypdfium2_raw/libpdfium.dylib
    pypdfium2_raw/pdfium.dll
Copyright:
    2024 PDFium developers
    2024 Developers of projects mentioned in PdfiumThirdParty
    2024 Beno<PERSON><PERSON> and pdfium-binaries contributors
License: (Apache-2.0 OR BSD-3-Clause) AND LicenseRef-PdfiumThirdParty
