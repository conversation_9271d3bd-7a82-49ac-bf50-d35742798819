# 安装和使用指南

## 🚀 快速开始

### 1. 安装依赖包

```bash
# 方法1: 使用requirements.txt（推荐）
pip install -r requirements.txt

# 方法2: 手动安装核心依赖
pip install paddlepaddle paddleocr opencv-python numpy pillow
```

### 2. 验证安装

```bash
# 运行快速测试
python quick_test.py
```

### 3. 基本使用

```bash
# 处理单张图片
python message_button_detector.py "doc\wechat_2025-08-05_232102_125.png"

# 查看帮助
python message_button_detector.py --help
```

## 📋 详细安装步骤

### 步骤1: 检查Python环境

确保您的Python版本 >= 3.7：

```bash
python --version
```

### 步骤2: 安装依赖

**选项A: 一键安装（推荐）**
```bash
pip install -r requirements.txt
```

**选项B: 分步安装**
```bash
# 核心依赖
pip install paddlepaddle>=2.4.0
pip install paddleocr>=2.6.0
pip install opencv-python>=4.5.0
pip install numpy>=1.21.0
pip install Pillow>=8.0.0

# 可选：GPU加速（需要NVIDIA GPU）
pip install paddlepaddle-gpu>=2.4.0
```

### 步骤3: 验证安装

运行测试脚本验证所有功能：

```bash
python quick_test.py
```

预期输出：
```
🚀 快速功能测试
========================================
🧪 测试文件结构...
✅ message_button_detector.py
✅ config.py
✅ requirements.txt
✅ README.md
✅ 样本图片目录存在，包含 4 张图片

🧪 测试配置文件...
✅ 配置文件导入成功
  - 目标关键词: ['发消息', '发送消息', '发送', '消息', 'Send', 'Message', '发信息']
  - 输出目录: output

🧪 测试OpenCV基本功能...
✅ OpenCV图片读写功能正常

🧪 测试模块导入...
✅ OpenCV导入成功
✅ NumPy导入成功
✅ Pillow导入成功
⏳ 正在导入PaddleOCR...
✅ PaddleOCR导入成功

========================================
📊 测试结果: 4/4 通过
🎉 基本功能测试通过！
```

## 🎯 使用示例

### 命令行使用

```bash
# 基本用法
python message_button_detector.py "path/to/image.png"

# 自定义输出目录
python message_button_detector.py "image.png" --output-dir "my_output"

# 只检测不保存标注图片
python message_button_detector.py "image.png" --no-annotate

# 不保存JSON结果
python message_button_detector.py "image.png" --no-json
```

### Python API使用

```python
from message_button_detector import MessageButtonDetector

# 创建检测器
detector = MessageButtonDetector()

# 处理图片
result = detector.process_image('doc/wechat_example.png')

# 查看结果
if result['success']:
    print(f"检测到 {result['total_buttons']} 个按钮")
    for button in result['buttons']:
        print(f"按钮: '{button['text']}' at {button['center']}")
```

### 批量处理示例

```python
# 运行示例脚本
python example_usage.py
```

## ⚠️ 常见问题解决

### Q1: 首次运行很慢？

**原因**: PaddleOCR首次使用需要下载模型文件（约100MB）

**解决**: 
- 确保网络连接正常
- 耐心等待模型下载完成（通常5-10分钟）
- 下载完成后后续使用会很快

### Q2: 提示"No module named 'paddleocr'"？

**解决**:
```bash
pip install paddleocr
# 或者
pip install -r requirements.txt
```

### Q3: OpenCV相关错误？

**解决**:
```bash
pip uninstall opencv-python opencv-contrib-python opencv-python-headless
pip install opencv-python
```

### Q4: 内存不足错误？

**解决**:
- 处理较小的图片
- 关闭其他占用内存的程序
- 考虑使用GPU版本（如有GPU）

### Q5: 识别准确率不高？

**解决**:
1. 检查图片质量（清晰度、分辨率）
2. 调整配置文件中的参数：
   ```python
   # 在config.py中调整
   DETECTION_CONFIG = {
       'confidence_threshold': 0.3,  # 降低阈值
       'similarity_threshold': 0.6,  # 降低相似度要求
   }
   ```
3. 添加更多关键词：
   ```python
   TARGET_KEYWORDS.extend(['发送', '提交', 'Submit'])
   ```

## 🔧 高级配置

### GPU加速设置

如果您有NVIDIA GPU：

1. 安装GPU版本：
   ```bash
   pip install paddlepaddle-gpu
   ```

2. 修改配置：
   ```python
   # 在config.py中设置
   OCR_CONFIG = {
       'use_gpu': True,  # 启用GPU
       # 其他配置...
   }
   ```

### 自定义识别关键词

```python
# 在config.py中添加
TARGET_KEYWORDS = [
    '发消息', '发送消息', '发送', '消息',
    'Send', 'Message', '发信息',
    # 添加您的自定义关键词
    '提交', '确认', 'Submit', 'Confirm', '发布'
]
```

### 调整输出格式

```python
# 在config.py中修改
IMAGE_CONFIG = {
    'output_dir': 'my_output',        # 自定义输出目录
    'bbox_color': (255, 0, 0),        # 红色边界框
    'bbox_thickness': 3,              # 更粗的线条
    'text_color': (0, 255, 0),        # 绿色文字
    'text_scale': 1.0,                # 更大的文字
}
```

## 📁 项目文件说明

```
发消息图像识别/
├── message_button_detector.py  # 主脚本（核心功能）
├── config.py                   # 配置文件
├── requirements.txt            # 依赖列表
├── example_usage.py           # 使用示例
├── quick_test.py              # 快速测试
├── test_detector.py           # 完整测试
├── README.md                  # 详细说明
├── INSTALL_GUIDE.md           # 本安装指南
├── doc/                       # 测试图片
└── output/                    # 输出目录（自动创建）
```

## 🎉 安装完成

如果所有测试都通过，您就可以开始使用图像识别脚本了！

**下一步**:
1. 将您的截图放入 `doc/` 目录
2. 运行: `python message_button_detector.py doc/your_image.png`
3. 查看 `output/` 目录中的结果

**获取帮助**:
- 查看 `README.md` 了解详细功能
- 运行 `python example_usage.py` 查看使用示例
- 遇到问题请检查本指南的常见问题部分
