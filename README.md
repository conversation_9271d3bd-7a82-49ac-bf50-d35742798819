# 图像识别脚本 - "发消息"按钮检测器

一个基于PaddleOCR和OpenCV的智能图像识别脚本，专门用于识别聊天应用界面中的"发消息"按钮，并提供精确的位置标注。

## ✨ 功能特性

- 🎯 **精准识别**: 使用PaddleOCR进行中文文字识别，支持多种"发消息"相关关键词
- 🖼️ **智能标注**: 在原图上绘制识别结果，包括边界框、中心点和置信度
- 📊 **多格式输出**: 支持图片标注和JSON数据导出
- 🔧 **灵活配置**: 可自定义识别关键词、输出格式和处理参数
- 📱 **广泛兼容**: 适用于微信、QQ、钉钉等各种聊天应用界面
- ⚡ **高性能**: 优化的图像处理流程，快速准确的识别结果

## 🛠️ 技术栈

- **PaddleOCR**: 百度开源的OCR工具，优秀的中文识别能力
- **OpenCV**: 图像处理和计算机视觉库
- **NumPy**: 数值计算支持
- **Pillow**: 图像处理辅助

## 📦 安装依赖

### 方法1: 使用pip安装（推荐）

```bash
# 安装所有依赖
pip install -r requirements.txt
```

### 方法2: 手动安装

```bash
pip install paddlepaddle>=2.4.0
pip install paddleocr>=2.6.0
pip install opencv-python>=4.5.0
pip install numpy>=1.21.0
pip install Pillow>=8.0.0
```

### GPU加速（可选）

如果您有NVIDIA GPU，可以安装GPU版本以获得更好的性能：

```bash
pip install paddlepaddle-gpu>=2.4.0
```

然后在 `config.py` 中设置 `use_gpu: True`

## 🚀 快速开始

### 命令行使用

```bash
# 基本用法
python message_button_detector.py path/to/your/image.png

# 自定义输出目录
python message_button_detector.py image.png --output-dir custom_output

# 只检测不保存标注图片
python message_button_detector.py image.png --no-annotate

# 不保存JSON结果
python message_button_detector.py image.png --no-json
```

### Python API使用

```python
from message_button_detector import MessageButtonDetector

# 创建检测器
detector = MessageButtonDetector()

# 处理单张图片
result = detector.process_image('path/to/image.png')

# 仅检测不保存文件
buttons = detector.detect_message_buttons('path/to/image.png')

# 手动绘制标注
if buttons:
    output_path = detector.draw_annotations('path/to/image.png', buttons)
```

## 📁 项目结构

```
发消息图像识别/
├── message_button_detector.py  # 主脚本文件
├── config.py                   # 配置文件
├── requirements.txt            # 依赖列表
├── example_usage.py           # 使用示例
├── README.md                  # 说明文档
├── doc/                       # 测试图片目录
│   ├── wechat_*.png          # 微信截图样本
│   └── ...
└── output/                    # 输出目录（自动创建）
    ├── *_annotated.jpg       # 标注后的图片
    └── detection_results.json # 检测结果JSON
```

## ⚙️ 配置说明

在 `config.py` 中可以自定义以下参数：

### OCR配置
- `use_angle_cls`: 是否使用角度分类器
- `lang`: 语言设置（'ch'中文, 'en'英文）
- `use_gpu`: 是否使用GPU加速
- `show_log`: 是否显示PaddleOCR日志

### 识别关键词
```python
TARGET_KEYWORDS = [
    '发消息', '发送消息', '发送', '消息',
    'Send', 'Message', '发信息'
]
```

### 图像处理参数
- `output_dir`: 输出目录
- `bbox_color`: 边界框颜色
- `bbox_thickness`: 边界框线条粗细
- `text_color`: 文字颜色
- `confidence_threshold`: 置信度阈值

## 📊 输出格式

### 控制台输出
```
==================================================
🔍 图像识别结果摘要
==================================================
📁 处理图片: doc/wechat_example.png
🎯 检测到按钮数量: 2

📍 按钮详细信息:
  1. 文字: '发消息'
     位置: (850, 600)
     尺寸: 80×35 像素
     置信度: 0.95

📂 输出文件:
  - annotated_image: output/wechat_example_annotated.jpg
  - json_results: output/detection_results.json
==================================================
```

### JSON输出格式
```json
{
  "total_buttons": 1,
  "buttons": [
    {
      "id": 1,
      "text": "发消息",
      "confidence": 0.952,
      "position": {
        "center_x": 850,
        "center_y": 600,
        "width": 80,
        "height": 35
      },
      "bbox_coordinates": [[810, 582], [890, 582], [890, 618], [810, 618]]
    }
  ]
}
```

## 🎯 使用场景

1. **UI自动化测试**: 定位聊天应用中的发送按钮
2. **界面分析**: 分析不同应用的界面布局
3. **辅助功能开发**: 为视障用户提供界面元素定位
4. **数据采集**: 批量分析聊天界面截图

## 🔧 高级用法

### 批量处理
```python
import os
from message_button_detector import MessageButtonDetector

detector = MessageButtonDetector()

# 处理目录下所有图片
for filename in os.listdir('images/'):
    if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
        image_path = os.path.join('images/', filename)
        result = detector.process_image(image_path)
        print(f"处理 {filename}: {result['total_buttons']} 个按钮")
```

### 自定义关键词
```python
from config import TARGET_KEYWORDS

# 添加自定义关键词
TARGET_KEYWORDS.extend(['提交', '确认', 'Submit', 'Confirm'])
```

## ⚠️ 注意事项

1. **图片质量**: 确保输入图片清晰，文字可读
2. **中文支持**: PaddleOCR对中文支持良好，但需要网络下载模型（首次使用）
3. **内存使用**: 处理大图片时可能占用较多内存
4. **GPU加速**: 如有GPU可显著提升处理速度

## 🐛 常见问题

### Q: 首次运行很慢？
A: PaddleOCR首次使用需要下载模型文件，请耐心等待。

### Q: 识别准确率不高？
A: 可以调整 `config.py` 中的 `confidence_threshold` 和添加更多关键词。

### Q: 无法识别某些按钮？
A: 检查按钮文字是否在 `TARGET_KEYWORDS` 列表中，可以添加相应关键词。

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**开发者**: AI Assistant  
**版本**: 1.0.0  
**更新时间**: 2025-08-05
