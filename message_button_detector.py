"""
图像识别脚本 - 识别"发消息"按钮
作者: AI Assistant
功能: 使用OCR技术识别图片中的"发消息"按钮并标注位置
"""

import os
import cv2
import numpy as np
from paddleocr import PaddleOCR
from PIL import Image, ImageDraw, ImageFont
import json
import logging
from typing import List, Tuple, Dict, Optional
from config import OCR_CONFIG, TARGET_KEYWORDS, IMAGE_CONFIG, DETECTION_CONFIG

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MessageButtonDetector:
    """发消息按钮检测器"""
    
    def __init__(self):
        """初始化检测器"""
        try:
            self.ocr = PaddleOCR(**OCR_CONFIG)
            logger.info("PaddleOCR初始化成功")
        except Exception as e:
            logger.error(f"PaddleOCR初始化失败: {e}")
            raise
        
        # 创建输出目录
        os.makedirs(IMAGE_CONFIG['output_dir'], exist_ok=True)
    
    def detect_message_buttons(self, image_path: str) -> List[Dict]:
        """
        检测图片中的发消息按钮
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            检测结果列表，每个元素包含位置和文字信息
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
        logger.info(f"开始处理图片: {image_path}")
        
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图片: {image_path}")
        
        # OCR识别
        try:
            results = self.ocr.ocr(image_path, cls=True)
            logger.info(f"OCR识别完成，检测到 {len(results[0]) if results[0] else 0} 个文字区域")
        except Exception as e:
            logger.error(f"OCR识别失败: {e}")
            return []
        
        # 筛选目标按钮
        detected_buttons = []
        if results[0]:
            for line in results[0]:
                bbox, (text, confidence) = line
                
                # 检查置信度
                if confidence < DETECTION_CONFIG['confidence_threshold']:
                    continue
                
                # 检查是否包含目标关键词
                if self._is_target_text(text):
                    button_info = {
                        'text': text,
                        'confidence': confidence,
                        'bbox': bbox,
                        'center': self._get_bbox_center(bbox),
                        'size': self._get_bbox_size(bbox)
                    }
                    detected_buttons.append(button_info)
                    logger.info(f"检测到目标按钮: '{text}' (置信度: {confidence:.2f})")
        
        return detected_buttons

    def _is_target_text(self, text: str) -> bool:
        """检查文字是否为目标关键词"""
        text = text.strip().lower()
        for keyword in TARGET_KEYWORDS:
            if keyword.lower() in text:
                return True
        return False

    def _get_bbox_center(self, bbox: List) -> Tuple[int, int]:
        """获取边界框中心点"""
        x_coords = [point[0] for point in bbox]
        y_coords = [point[1] for point in bbox]
        center_x = int(sum(x_coords) / len(x_coords))
        center_y = int(sum(y_coords) / len(y_coords))
        return (center_x, center_y)

    def _get_bbox_size(self, bbox: List) -> Tuple[int, int]:
        """获取边界框尺寸"""
        x_coords = [point[0] for point in bbox]
        y_coords = [point[1] for point in bbox]
        width = int(max(x_coords) - min(x_coords))
        height = int(max(y_coords) - min(y_coords))
        return (width, height)

    def draw_annotations(self, image_path: str, detected_buttons: List[Dict],
                        output_path: Optional[str] = None) -> str:
        """
        在图片上绘制检测结果标注

        Args:
            image_path: 原始图片路径
            detected_buttons: 检测结果
            output_path: 输出路径（可选）

        Returns:
            输出文件路径
        """
        # 读取图片
        image = cv2.imread(image_path)

        # 绘制标注
        for i, button in enumerate(detected_buttons):
            bbox = button['bbox']
            text = button['text']
            confidence = button['confidence']

            # 转换bbox为矩形坐标
            points = np.array(bbox, dtype=np.int32)

            # 绘制边界框
            cv2.polylines(image, [points], True, IMAGE_CONFIG['bbox_color'],
                         IMAGE_CONFIG['bbox_thickness'])

            # 绘制文字标签
            label = f"{text} ({confidence:.2f})"
            label_pos = (int(bbox[0][0]), int(bbox[0][1]) - 10)

            cv2.putText(image, label, label_pos, cv2.FONT_HERSHEY_SIMPLEX,
                       IMAGE_CONFIG['text_scale'], IMAGE_CONFIG['text_color'],
                       IMAGE_CONFIG['text_thickness'])

            # 绘制中心点
            center = button['center']
            cv2.circle(image, center, 5, (0, 0, 255), -1)

        # 保存标注后的图片
        if output_path is None:
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            output_path = os.path.join(IMAGE_CONFIG['output_dir'],
                                     f"{base_name}_annotated.jpg")

        cv2.imwrite(output_path, image)
        logger.info(f"标注图片已保存: {output_path}")
        return output_path

    def save_results_to_json(self, detected_buttons: List[Dict],
                           output_path: Optional[str] = None) -> str:
        """
        保存检测结果到JSON文件

        Args:
            detected_buttons: 检测结果
            output_path: 输出路径（可选）

        Returns:
            输出文件路径
        """
        if output_path is None:
            output_path = os.path.join(IMAGE_CONFIG['output_dir'], 'detection_results.json')

        # 格式化结果数据
        results_data = {
            'total_buttons': len(detected_buttons),
            'buttons': []
        }

        for i, button in enumerate(detected_buttons):
            button_data = {
                'id': i + 1,
                'text': button['text'],
                'confidence': round(button['confidence'], 3),
                'position': {
                    'center_x': button['center'][0],
                    'center_y': button['center'][1],
                    'width': button['size'][0],
                    'height': button['size'][1]
                },
                'bbox_coordinates': button['bbox']
            }
            results_data['buttons'].append(button_data)

        # 保存到JSON文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, ensure_ascii=False, indent=2)

        logger.info(f"检测结果已保存到JSON: {output_path}")
        return output_path

    def process_image(self, image_path: str, save_annotated: bool = True,
                     save_json: bool = True) -> Dict:
        """
        完整处理图片的主方法

        Args:
            image_path: 图片路径
            save_annotated: 是否保存标注图片
            save_json: 是否保存JSON结果

        Returns:
            处理结果字典
        """
        try:
            # 检测按钮
            detected_buttons = self.detect_message_buttons(image_path)

            result = {
                'success': True,
                'image_path': image_path,
                'total_buttons': len(detected_buttons),
                'buttons': detected_buttons,
                'output_files': {}
            }

            # 保存标注图片
            if save_annotated and detected_buttons:
                annotated_path = self.draw_annotations(image_path, detected_buttons)
                result['output_files']['annotated_image'] = annotated_path

            # 保存JSON结果
            if save_json and detected_buttons:
                json_path = self.save_results_to_json(detected_buttons)
                result['output_files']['json_results'] = json_path

            # 打印结果摘要
            self._print_summary(result)

            return result

        except Exception as e:
            logger.error(f"处理图片时发生错误: {e}")
            return {
                'success': False,
                'error': str(e),
                'image_path': image_path
            }

    def _print_summary(self, result: Dict):
        """打印处理结果摘要"""
        print("\n" + "="*50)
        print("🔍 图像识别结果摘要")
        print("="*50)
        print(f"📁 处理图片: {result['image_path']}")
        print(f"🎯 检测到按钮数量: {result['total_buttons']}")

        if result['total_buttons'] > 0:
            print("\n📍 按钮详细信息:")
            for i, button in enumerate(result['buttons'], 1):
                center = button['center']
                size = button['size']
                print(f"  {i}. 文字: '{button['text']}'")
                print(f"     位置: ({center[0]}, {center[1]})")
                print(f"     尺寸: {size[0]}×{size[1]} 像素")
                print(f"     置信度: {button['confidence']:.2f}")
                print()

        if 'output_files' in result and result['output_files']:
            print("📂 输出文件:")
            for file_type, file_path in result['output_files'].items():
                print(f"  - {file_type}: {file_path}")

        print("="*50)


def main():
    """主函数 - 命令行接口"""
    import argparse

    parser = argparse.ArgumentParser(description='图像识别脚本 - 识别"发消息"按钮')
    parser.add_argument('image_path', help='输入图片路径')
    parser.add_argument('--no-annotate', action='store_true', help='不保存标注图片')
    parser.add_argument('--no-json', action='store_true', help='不保存JSON结果')
    parser.add_argument('--output-dir', help='自定义输出目录')

    args = parser.parse_args()

    # 自定义输出目录
    if args.output_dir:
        IMAGE_CONFIG['output_dir'] = args.output_dir

    # 创建检测器并处理图片
    detector = MessageButtonDetector()
    result = detector.process_image(
        image_path=args.image_path,
        save_annotated=not args.no_annotate,
        save_json=not args.no_json
    )

    # 返回状态码
    return 0 if result['success'] else 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
